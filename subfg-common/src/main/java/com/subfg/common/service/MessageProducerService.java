package com.subfg.common.service;

import java.util.Map;
import java.util.UUID;

import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import com.subfg.common.config.RabbitMQConfig;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息生产者服务
 * 提供各种类型的消息发送功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageProducerService {

    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送用户通知消息
     *
     * @param userId  用户ID
     * @param title   通知标题
     * @param content 通知内容
     * @param type    通知类型
     */
    public void sendUserNotification(String userId, String title, String content, Integer type) {
        try {
            UserNotificationMessage message = UserNotificationMessage.builder()
                    .userId(userId)
                    .title(title)
                    .content(content)
                    .type(type)
                    .timestamp(System.currentTimeMillis())
                    .build();

            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.DIRECT_EXCHANGE,
                    RabbitMQConfig.USER_NOTIFICATION_ROUTING_KEY,
                    message,
                    correlationData
            );
            
            log.info("用户通知消息发送成功 - userId: {}, title: {}", userId, title);
        } catch (Exception e) {
            log.error("用户通知消息发送失败 - userId: {}, title: {}", userId, title, e);
            throw new RuntimeException("用户通知消息发送失败", e);
        }
    }

    /**
     * 发送邮件消息
     *
     * @param to      收件人
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param type    邮件类型（1-文本，2-HTML）
     */
    public void sendEmailMessage(String to, String subject, String content, Integer type) {
        try {
            EmailMessage message = EmailMessage.builder()
                    .to(to)
                    .subject(subject)
                    .content(content)
                    .type(type)
                    .timestamp(System.currentTimeMillis())
                    .build();

            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.DIRECT_EXCHANGE,
                    RabbitMQConfig.EMAIL_ROUTING_KEY,
                    message,
                    correlationData
            );
            
            log.info("邮件消息发送成功 - to: {}, subject: {}", to, subject);
        } catch (Exception e) {
            log.error("邮件消息发送失败 - to: {}, subject: {}", to, subject, e);
            throw new RuntimeException("邮件消息发送失败", e);
        }
    }

    /**
     * 发送系统日志消息
     *
     * @param level   日志级别
     * @param module  模块名称
     * @param action  操作名称
     * @param message 日志消息
     * @param userId  用户ID（可选）
     */
    public void sendSystemLogMessage(String level, String module, String action, String message, String userId) {
        try {
            SystemLogMessage logMessage = SystemLogMessage.builder()
                    .level(level)
                    .module(module)
                    .action(action)
                    .message(message)
                    .userId(userId)
                    .timestamp(System.currentTimeMillis())
                    .build();

            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.TOPIC_EXCHANGE,
                    RabbitMQConfig.SYSTEM_LOG_ROUTING_KEY,
                    logMessage,
                    correlationData
            );
            
            log.debug("系统日志消息发送成功 - module: {}, action: {}", module, action);
        } catch (Exception e) {
            log.error("系统日志消息发送失败 - module: {}, action: {}", module, action, e);
            // 日志消息发送失败不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 发送广播消息
     *
     * @param messageType 消息类型
     * @param data        消息数据
     */
    public void sendBroadcastMessage(String messageType, Map<String, Object> data) {
        try {
            BroadcastMessage message = BroadcastMessage.builder()
                    .messageType(messageType)
                    .data(data)
                    .timestamp(System.currentTimeMillis())
                    .build();

            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.FANOUT_EXCHANGE,
                    "", // 扇形交换机不需要路由键
                    message,
                    correlationData
            );
            
            log.info("广播消息发送成功 - messageType: {}", messageType);
        } catch (Exception e) {
            log.error("广播消息发送失败 - messageType: {}", messageType, e);
            throw new RuntimeException("广播消息发送失败", e);
        }
    }

    /**
     * 发送延时消息
     *
     * @param exchange    交换机
     * @param routingKey  路由键
     * @param message     消息内容
     * @param delayMillis 延时毫秒数
     */
    public void sendDelayMessage(String exchange, String routingKey, Object message, long delayMillis) {
        try {
            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(exchange, routingKey, message, msg -> {
                // 设置消息延时
                msg.getMessageProperties().setDelayLong((int) delayMillis);
                return msg;
            }, correlationData);
            
            log.info("延时消息发送成功 - exchange: {}, routingKey: {}, delay: {}ms", 
                    exchange, routingKey, delayMillis);
        } catch (Exception e) {
            log.error("延时消息发送失败 - exchange: {}, routingKey: {}", exchange, routingKey, e);
            throw new RuntimeException("延时消息发送失败", e);
        }
    }

    /**
     * 发送带优先级的消息
     *
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param message    消息内容
     * @param priority   优先级（0-255）
     */
    public void sendPriorityMessage(String exchange, String routingKey, Object message, int priority) {
        try {
            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            rabbitTemplate.convertAndSend(exchange, routingKey, message, msg -> {
                // 设置消息优先级
                msg.getMessageProperties().setPriority(priority);
                return msg;
            }, correlationData);
            
            log.info("优先级消息发送成功 - exchange: {}, routingKey: {}, priority: {}", 
                    exchange, routingKey, priority);
        } catch (Exception e) {
            log.error("优先级消息发送失败 - exchange: {}, routingKey: {}", exchange, routingKey, e);
            throw new RuntimeException("优先级消息发送失败", e);
        }
    }

    // ==================== 内部消息类定义 ====================

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserNotificationMessage {
        private String userId;
        private String title;
        private String content;
        private Integer type;
        private Long timestamp;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class EmailMessage {
        private String to;
        private String subject;
        private String content;
        private Integer type;
        private Long timestamp;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SystemLogMessage {
        private String level;
        private String module;
        private String action;
        private String message;
        private String userId;
        private Long timestamp;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class BroadcastMessage {
        private String messageType;
        private Map<String, Object> data;
        private Long timestamp;
    }
}
