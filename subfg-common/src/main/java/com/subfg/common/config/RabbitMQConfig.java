package com.subfg.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ配置类
 * 配置交换机、队列、绑定关系以及消息转换器
 */
@Slf4j
@Configuration
public class RabbitMQConfig {

    // ==================== 交换机定义 ====================
    
    /**
     * 直连交换机 - 用于点对点消息传递
     */
    public static final String DIRECT_EXCHANGE = "subfg.direct.exchange";
    
    /**
     * 主题交换机 - 用于基于路由键模式匹配的消息传递
     */
    public static final String TOPIC_EXCHANGE = "subfg.topic.exchange";
    
    /**
     * 扇形交换机 - 用于广播消息
     */
    public static final String FANOUT_EXCHANGE = "subfg.fanout.exchange";

    // ==================== 队列定义 ====================
    
    /**
     * 用户通知队列
     */
    public static final String USER_NOTIFICATION_QUEUE = "subfg.user.notification.queue";
    
    /**
     * 邮件发送队列
     */
    public static final String EMAIL_QUEUE = "subfg.email.queue";
    
    /**
     * 系统日志队列
     */
    public static final String SYSTEM_LOG_QUEUE = "subfg.system.log.queue";
    
    /**
     * 死信队列
     */
    public static final String DEAD_LETTER_QUEUE = "subfg.dead.letter.queue";

    // ==================== 路由键定义 ====================
    
    /**
     * 用户通知路由键
     */
    public static final String USER_NOTIFICATION_ROUTING_KEY = "user.notification";
    
    /**
     * 邮件发送路由键
     */
    public static final String EMAIL_ROUTING_KEY = "email.send";
    
    /**
     * 系统日志路由键
     */
    public static final String SYSTEM_LOG_ROUTING_KEY = "system.log";

    // ==================== 交换机Bean配置 ====================

    /**
     * 配置直连交换机
     */
    @Bean
    public DirectExchange directExchange() {
        return ExchangeBuilder
                .directExchange(DIRECT_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 配置主题交换机
     */
    @Bean
    public TopicExchange topicExchange() {
        return ExchangeBuilder
                .topicExchange(TOPIC_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 配置扇形交换机
     */
    @Bean
    public FanoutExchange fanoutExchange() {
        return ExchangeBuilder
                .fanoutExchange(FANOUT_EXCHANGE)
                .durable(true)
                .build();
    }

    // ==================== 队列Bean配置 ====================

    /**
     * 配置用户通知队列
     */
    @Bean
    public Queue userNotificationQueue() {
        return QueueBuilder
                .durable(USER_NOTIFICATION_QUEUE)
                .withArgument("x-dead-letter-exchange", DIRECT_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", "dead.letter")
                .build();
    }

    /**
     * 配置邮件发送队列
     */
    @Bean
    public Queue emailQueue() {
        return QueueBuilder
                .durable(EMAIL_QUEUE)
                .withArgument("x-dead-letter-exchange", DIRECT_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", "dead.letter")
                .build();
    }

    /**
     * 配置系统日志队列
     */
    @Bean
    public Queue systemLogQueue() {
        return QueueBuilder
                .durable(SYSTEM_LOG_QUEUE)
                .withArgument("x-dead-letter-exchange", DIRECT_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", "dead.letter")
                .build();
    }

    /**
     * 配置死信队列
     */
    @Bean
    public Queue deadLetterQueue() {
        return QueueBuilder
                .durable(DEAD_LETTER_QUEUE)
                .build();
    }

    // ==================== 绑定关系配置 ====================

    /**
     * 绑定用户通知队列到直连交换机
     */
    @Bean
    public Binding userNotificationBinding() {
        return BindingBuilder
                .bind(userNotificationQueue())
                .to(directExchange())
                .with(USER_NOTIFICATION_ROUTING_KEY);
    }

    /**
     * 绑定邮件队列到直连交换机
     */
    @Bean
    public Binding emailBinding() {
        return BindingBuilder
                .bind(emailQueue())
                .to(directExchange())
                .with(EMAIL_ROUTING_KEY);
    }

    /**
     * 绑定系统日志队列到主题交换机
     */
    @Bean
    public Binding systemLogBinding() {
        return BindingBuilder
                .bind(systemLogQueue())
                .to(topicExchange())
                .with(SYSTEM_LOG_ROUTING_KEY);
    }

    /**
     * 绑定死信队列到直连交换机
     */
    @Bean
    public Binding deadLetterBinding() {
        return BindingBuilder
                .bind(deadLetterQueue())
                .to(directExchange())
                .with("dead.letter");
    }

    // ==================== 消息转换器和模板配置 ====================

    /**
     * 配置JSON消息转换器
     */
    @Bean
    public MessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /**
     * 配置RabbitTemplate
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter());
        
        // 设置发布确认回调
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("消息发送成功，correlationData: {}", correlationData);
            } else {
                log.error("消息发送失败，correlationData: {}, cause: {}", correlationData, cause);
            }
        });
        
        // 设置发布返回回调
        template.setReturnsCallback(returned -> {
            log.error("消息被退回，exchange: {}, routingKey: {}, replyCode: {}, replyText: {}", 
                    returned.getExchange(), returned.getRoutingKey(), 
                    returned.getReplyCode(), returned.getReplyText());
        });
        
        return template;
    }

    /**
     * 配置监听器容器工厂
     */
    @Bean
    public RabbitListenerContainerFactory<?> rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter());
        
        // 设置并发消费者数量
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        
        // 设置预取数量
        factory.setPrefetchCount(1);
        
        return factory;
    }
}
